{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "jsx": "preserve", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["node", "react", "react-dom"], "noImplicitAny": false}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules"]}