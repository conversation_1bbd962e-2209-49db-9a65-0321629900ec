# Installation
> `npm install --save @types/react-map-gl`

# Summary
This package contains type definitions for react-map-gl (https://github.com/visgl/react-map-gl#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-map-gl.

### Additional Details
 * Last updated: Thu, 05 Dec 2024 19:32:25 GMT
 * Dependencies: [@types/geojson](https://npmjs.com/package/@types/geojson), [@types/mapbox-gl](https://npmjs.com/package/@types/mapbox-gl), [@types/react](https://npmjs.com/package/@types/react), [@types/viewport-mercator-project](https://npmjs.com/package/@types/viewport-mercator-project)

# Credits
These definitions were written by [<PERSON>](https://github.com/rimig), [<PERSON><PERSON><PERSON>](https://github.com/fnberta), [<PERSON><PERSON>](https://github.com/sandersiim), [<PERSON><PERSON>](https://github.com/Arman92), [<PERSON>](https://github.com/chiuhow), [<PERSON>gold](https://github.com/singingwolfboy), [Ilja Reznik](https://github.com/ireznik), and [Arthur Cheung](https://github.com/arthur-cheung).
