{"name": "@types/viewport-mercator-project", "version": "6.1.6", "description": "TypeScript definitions for viewport-mercator-project", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/viewport-mercator-project", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "fnberta", "url": "https://github.com/fnberta"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/viewport-mercator-project"}, "scripts": {}, "dependencies": {"gl-matrix": "^3.2.0"}, "typesPublisherContentHash": "c5e0bf416dbc03fb453c291b3f5e82496f4bbb0e3e819157a642b120d5cc2793", "typeScriptVersion": "4.5"}