{"name": "@types/react-map-gl", "version": "6.1.7", "description": "TypeScript definitions for react-map-gl", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-map-gl", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON>ig", "url": "https://github.com/rimig"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "fnberta", "url": "https://github.com/fnberta"}, {"name": "<PERSON><PERSON>", "githubUsername": "sandersiim", "url": "https://github.com/sandersiim"}, {"name": "<PERSON><PERSON>", "githubUsername": "Arman92", "url": "https://github.com/Arman92"}, {"name": "<PERSON>", "githubUsername": "chiuhow", "url": "https://github.com/chiuhow"}, {"name": "<PERSON>", "githubUsername": "singing<PERSON><PERSON>", "url": "https://github.com/singingwolfboy"}, {"name": "<PERSON>ja <PERSON>", "githubUsername": "ireznik", "url": "https://github.com/ireznik"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>eu<PERSON>", "url": "https://github.com/arthur-cheung"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-map-gl"}, "scripts": {}, "dependencies": {"@types/geojson": "*", "@types/mapbox-gl": "*", "@types/react": "*", "@types/viewport-mercator-project": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d7dc1a3ceee06f60eba2ba9f5f761cb24debba9c40368a4432fdd1f12aa5e8d5", "typeScriptVersion": "5.0"}